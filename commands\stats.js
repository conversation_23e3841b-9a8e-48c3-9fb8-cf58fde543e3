const { EmbedBuilder } = require('discord.js');
const Storage = require('../utils/storage');

module.exports = {
    name: 'stats',
    description: 'Affiche les statistiques du bot (multi-serveurs)',
    usage: '!stats',
    
    async execute(message, args) {
        try {
            // Vérification des permissions (optionnel - peut être utilisé par tous)
            const storage = new Storage();
            const client = message.client;
            
            // Récupération de toutes les données
            const allLinks = await storage.getAllLinks();
            const totalLinks = Object.keys(allLinks).length;
            
            // Statistiques générales du bot
            const totalGuilds = client.guilds.cache.size;
            const totalUsers = client.users.cache.size;
            const totalChannels = client.channels.cache.size;
            
            // Analyse des liens par serveur
            const guildStats = new Map();
            
            for (const [channelId, linkData] of Object.entries(allLinks)) {
                try {
                    const channel = await client.channels.fetch(channelId);
                    if (channel && channel.guild) {
                        const guildName = channel.guild.name;
                        const guildId = channel.guild.id;
                        
                        if (!guildStats.has(guildId)) {
                            guildStats.set(guildId, {
                                name: guildName,
                                channels: [],
                                linkCount: 0
                            });
                        }
                        
                        const guildData = guildStats.get(guildId);
                        guildData.channels.push({
                            name: channel.name,
                            url: linkData.url,
                            addedAt: linkData.addedAt
                        });
                        guildData.linkCount++;
                    }
                } catch (error) {
                    // Canal supprimé ou inaccessible - on l'ignore
                    console.log(`⚠️ Canal ${channelId} inaccessible (probablement supprimé)`);
                }
            }
            
            // Création de l'embed principal
            const statsEmbed = new EmbedBuilder()
                .setColor(0x00ff00)
                .setTitle('📊 Statistiques du Bot Vinted')
                .setDescription('Aperçu global du fonctionnement multi-serveurs')
                .addFields(
                    {
                        name: '🤖 Bot Global',
                        value: `**Serveurs:** ${totalGuilds}\n**Utilisateurs:** ${totalUsers}\n**Canaux:** ${totalChannels}`,
                        inline: true
                    },
                    {
                        name: '📎 Liens Vinted',
                        value: `**Total:** ${totalLinks}\n**Serveurs actifs:** ${guildStats.size}\n**Moyenne/serveur:** ${guildStats.size > 0 ? Math.round(totalLinks / guildStats.size * 10) / 10 : 0}`,
                        inline: true
                    },
                    {
                        name: '⚡ Statut',
                        value: `**En ligne:** ✅\n**Multi-serveurs:** ✅\n**Stockage:** JSON`,
                        inline: true
                    }
                )
                .setFooter({
                    text: `Demandé par ${message.author.tag}`,
                    iconURL: message.author.displayAvatarURL()
                })
                .setTimestamp();
            
            // Ajout des détails par serveur (limité pour éviter la limite Discord)
            if (guildStats.size > 0) {
                let serverDetails = '';
                let serverCount = 0;
                
                for (const [guildId, guildData] of guildStats) {
                    if (serverCount >= 10) { // Limite pour éviter les embeds trop longs
                        serverDetails += `\n... et ${guildStats.size - serverCount} autres serveurs`;
                        break;
                    }
                    
                    const channelList = guildData.channels
                        .slice(0, 3) // Max 3 canaux par serveur
                        .map(ch => `#${ch.name}`)
                        .join(', ');
                    
                    const moreChannels = guildData.channels.length > 3 ? ` (+${guildData.channels.length - 3})` : '';
                    
                    serverDetails += `**${guildData.name}**\n`;
                    serverDetails += `├ Liens: ${guildData.linkCount}\n`;
                    serverDetails += `└ Canaux: ${channelList}${moreChannels}\n\n`;
                    
                    serverCount++;
                }
                
                if (serverDetails.length > 0) {
                    // Diviser en plusieurs fields si nécessaire
                    const maxFieldLength = 1024;
                    if (serverDetails.length <= maxFieldLength) {
                        statsEmbed.addFields({
                            name: '🏠 Détails par Serveur',
                            value: serverDetails,
                            inline: false
                        });
                    } else {
                        // Diviser en plusieurs fields
                        const parts = [];
                        let currentPart = '';
                        const lines = serverDetails.split('\n');
                        
                        for (const line of lines) {
                            if ((currentPart + line + '\n').length > maxFieldLength) {
                                if (currentPart) parts.push(currentPart);
                                currentPart = line + '\n';
                            } else {
                                currentPart += line + '\n';
                            }
                        }
                        if (currentPart) parts.push(currentPart);
                        
                        parts.forEach((part, index) => {
                            statsEmbed.addFields({
                                name: index === 0 ? '🏠 Détails par Serveur' : '🏠 Détails par Serveur (suite)',
                                value: part,
                                inline: false
                            });
                        });
                    }
                }
            } else {
                statsEmbed.addFields({
                    name: '🏠 Serveurs',
                    value: 'Aucun lien Vinted configuré pour le moment.\nUtilisez `!addLien <url>` pour commencer !',
                    inline: false
                });
            }
            
            await message.reply({ embeds: [statsEmbed] });
            
        } catch (error) {
            console.error('Erreur dans la commande stats:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor(0xff0000)
                .setTitle('❌ Erreur')
                .setDescription('Une erreur est survenue lors de la récupération des statistiques.')
                .setTimestamp();
            
            message.reply({ embeds: [errorEmbed] });
        }
    }
};
