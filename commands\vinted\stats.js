const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const Storage = require('../../utils/storage');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('stats')
        .setDescription('Affiche les statistiques du bot Vinted (multi-serveurs)'),

    async execute(interaction) {
        try {
            await interaction.deferReply();

            const storage = new Storage();
            const client = interaction.client;
            
            // Récupération de toutes les données
            const allConfigs = await storage.getAllLinks();
            const totalConfigs = Object.keys(allConfigs).length;
            
            // Statistiques générales du bot
            const totalGuilds = client.guilds.cache.size;
            const totalUsers = client.users.cache.size;
            const totalChannels = client.channels.cache.size;
            
            // Analyse des configurations par serveur
            const guildStats = new Map();
            
            for (const [channelId, configData] of Object.entries(allConfigs)) {
                try {
                    const channel = await client.channels.fetch(channelId);
                    if (channel && channel.guild) {
                        const guildName = channel.guild.name;
                        const guildId = channel.guild.id;
                        
                        if (!guildStats.has(guildId)) {
                            guildStats.set(guildId, {
                                name: guildName,
                                channels: [],
                                configCount: 0
                            });
                        }
                        
                        const guildData = guildStats.get(guildId);
                        guildData.channels.push({
                            name: channel.name,
                            productUrl: configData.productUrl || configData.url,
                            webhook: !!configData.webhook,
                            addedAt: configData.addedAt
                        });
                        guildData.configCount++;
                    }
                } catch (error) {
                    // Canal supprimé ou inaccessible - on l'ignore
                    console.log(`⚠️ Canal ${channelId} inaccessible (probablement supprimé)`);
                }
            }
            
            // Création de l'embed principal
            const statsEmbed = new EmbedBuilder()
                .setColor('#1abc9c')
                .setTitle('📊 Statistiques Vinted Finders')
                .setDescription('Aperçu global du fonctionnement multi-serveurs')
                .addFields(
                    {
                        name: '🤖 Bot Global',
                        value: `**Serveurs:** ${totalGuilds}\n**Utilisateurs:** ${totalUsers.toLocaleString()}\n**Canaux:** ${totalChannels.toLocaleString()}`,
                        inline: true
                    },
                    {
                        name: '🔗 Configurations Vinted',
                        value: `**Total:** ${totalConfigs}\n**Serveurs actifs:** ${guildStats.size}\n**Moyenne/serveur:** ${guildStats.size > 0 ? Math.round(totalConfigs / guildStats.size * 10) / 10 : 0}`,
                        inline: true
                    },
                    {
                        name: '⚡ Statut',
                        value: `**En ligne:** ✅\n**Multi-serveurs:** ✅\n**Slash Commands:** ✅`,
                        inline: true
                    }
                )
                .setThumbnail('https://cdn.discordapp.com/attachments/1089538587072151664/1100784979673354310/Logo_lsv1.png')
                .setFooter({
                    text: `Demandé par ${interaction.user.tag}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();
            
            // Ajout des détails par serveur (limité pour éviter la limite Discord)
            if (guildStats.size > 0) {
                let serverDetails = '';
                let serverCount = 0;
                
                for (const [guildId, guildData] of guildStats) {
                    if (serverCount >= 8) { // Limite pour éviter les embeds trop longs
                        serverDetails += `\n... et ${guildStats.size - serverCount} autres serveurs`;
                        break;
                    }
                    
                    const channelList = guildData.channels
                        .slice(0, 3) // Max 3 canaux par serveur
                        .map(ch => `#${ch.name} ${ch.webhook ? '🔗' : '❌'}`)
                        .join('\n├ ');
                    
                    const moreChannels = guildData.channels.length > 3 ? `\n├ ... +${guildData.channels.length - 3} autres` : '';
                    
                    serverDetails += `**${guildData.name}**\n`;
                    serverDetails += `├ Configs: ${guildData.configCount}\n`;
                    serverDetails += `├ ${channelList}${moreChannels}\n\n`;
                    
                    serverCount++;
                }
                
                if (serverDetails.length > 0) {
                    // Diviser en plusieurs fields si nécessaire
                    const maxFieldLength = 1024;
                    if (serverDetails.length <= maxFieldLength) {
                        statsEmbed.addFields({
                            name: '🏠 Détails par Serveur (🔗 = Webhook configuré)',
                            value: serverDetails,
                            inline: false
                        });
                    } else {
                        // Diviser en plusieurs fields
                        const parts = [];
                        let currentPart = '';
                        const lines = serverDetails.split('\n');
                        
                        for (const line of lines) {
                            if ((currentPart + line + '\n').length > maxFieldLength) {
                                if (currentPart) parts.push(currentPart);
                                currentPart = line + '\n';
                            } else {
                                currentPart += line + '\n';
                            }
                        }
                        if (currentPart) parts.push(currentPart);
                        
                        parts.forEach((part, index) => {
                            statsEmbed.addFields({
                                name: index === 0 ? '🏠 Détails par Serveur (🔗 = Webhook)' : '🏠 Détails par Serveur (suite)',
                                value: part,
                                inline: false
                            });
                        });
                    }
                }
            } else {
                statsEmbed.addFields({
                    name: '🏠 Serveurs',
                    value: 'Aucune configuration Vinted active pour le moment.\nUtilisez `/addlien` pour commencer !',
                    inline: false
                });
            }

            // Ajout d'informations sur les fonctionnalités
            statsEmbed.addFields({
                name: '🚀 Fonctionnalités',
                value: '• Scraping produits Vinted\n• Recherche produits similaires\n• Tri par prix croissant\n• Webhooks Discord\n• Multi-serveurs natif',
                inline: false
            });
            
            await interaction.editReply({ embeds: [statsEmbed] });
            
        } catch (error) {
            console.error('Erreur dans la commande stats:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Erreur')
                .setDescription('Une erreur est survenue lors de la récupération des statistiques.')
                .addFields({
                    name: 'Détails de l\'erreur',
                    value: `\`${error.message}\``,
                    inline: false
                })
                .setTimestamp();
            
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
