const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('stats')
        .setDescription('Affiche les statistiques du bot Vinted Finders'),

    async execute(interaction) {
        try {
            await interaction.deferReply();

            const client = interaction.client;

            // Statistiques générales du bot
            const totalGuilds = client.guilds.cache.size;
            const totalUsers = client.users.cache.size;
            const totalChannels = client.channels.cache.size;

            // Calcul de l'uptime
            const uptime = process.uptime();
            const days = Math.floor(uptime / 86400);
            const hours = Math.floor((uptime % 86400) / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const uptimeString = `${days}j ${hours}h ${minutes}m`;
            
            // Création de l'embed principal
            const statsEmbed = new EmbedBuilder()
                .setColor('#1abc9c')
                .setTitle('📊 Vinted Finders - Statistiques')
                .setDescription('Bot de recherche avancée sur Vinted')
                .addFields(
                    {
                        name: '🤖 Statistiques Globales',
                        value: `**Serveurs:** ${totalGuilds}\n**Utilisateurs:** ${totalUsers.toLocaleString()}\n**Canaux:** ${totalChannels.toLocaleString()}`,
                        inline: true
                    },
                    {
                        name: '⏱️ Uptime',
                        value: `**En ligne depuis:** ${uptimeString}\n**Statut:** 🟢 Opérationnel\n**Version:** 2.0.0`,
                        inline: true
                    },
                    {
                        name: '🔍 Fonctionnalités',
                        value: `**Recherche Vinted:** ✅\n**Tri par prix:** ✅\n**Multi-serveurs:** ✅`,
                        inline: true
                    }
                )
                .addFields(
                    {
                        name: '🚀 Commandes Disponibles',
                        value: '`/recherche` - Recherche les meilleurs prix\n`/stats` - Affiche ces statistiques',
                        inline: false
                    },
                    {
                        name: '💡 Exemple d\'Utilisation',
                        value: '`/recherche termes:"nike t-shirt" prix_max:50 taille:M`',
                        inline: false
                    }
                )
                .setThumbnail('https://cdn.discordapp.com/attachments/1089538587072151664/1100784979673354310/Logo_lsv1.png')
                .setFooter({
                    text: `Demandé par ${interaction.user.tag}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();
            
            // Ajout d'informations sur les performances
            const memoryUsage = process.memoryUsage();
            const memoryMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);

            statsEmbed.addFields({
                name: '⚙️ Performances',
                value: `**Mémoire utilisée:** ${memoryMB} MB\n**Latence:** ${client.ws.ping}ms\n**Node.js:** ${process.version}`,
                inline: true
            });

            // Informations sur les serveurs
            if (totalGuilds > 0) {
                let serverList = '';
                let count = 0;

                client.guilds.cache.forEach(guild => {
                    if (count < 5) { // Limiter à 5 serveurs
                        serverList += `• ${guild.name} (${guild.memberCount} membres)\n`;
                        count++;
                    }
                });

                if (client.guilds.cache.size > 5) {
                    serverList += `... et ${client.guilds.cache.size - 5} autres serveurs`;
                }

                if (serverList) {
                    statsEmbed.addFields({
                        name: '🏠 Serveurs Connectés',
                        value: serverList,
                        inline: false
                    });
                }
            }

            await interaction.editReply({ embeds: [statsEmbed] });
            
        } catch (error) {
            console.error('Erreur dans la commande stats:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Erreur')
                .setDescription('Une erreur est survenue lors de la récupération des statistiques.')
                .addFields({
                    name: 'Détails de l\'erreur',
                    value: `\`${error.message}\``,
                    inline: false
                })
                .setTimestamp();
            
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
