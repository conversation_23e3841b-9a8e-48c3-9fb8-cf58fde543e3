const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const VintedScraper = require('../../utils/vinted-scraper');
const WebhookManager = require('../../utils/webhook-manager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('addlien')
        .setDescription('Ajoute un lien produit Vinted et trouve des produits similaires au prix le plus bas')
        .addStringOption(option =>
            option.setName('produit_url')
                .setDescription('URL du produit Vinted à analyser')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('webhook_url')
                .setDescription('URL du webhook Discord pour recevoir les notifications')
                .setRequired(true)),

    async execute(interaction) {
        try {
            // Vérification des permissions
            if (!interaction.member.permissions.has('ManageMessages')) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff0000')
                    .setTitle('❌ Permission refusée')
                    .setDescription('Vous devez avoir la permission "Gérer les messages" pour utiliser cette commande.')
                    .setTimestamp();
                
                return interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const productUrl = interaction.options.getString('produit_url');
            const webhookUrl = interaction.options.getString('webhook_url');
            const channelId = interaction.channel.id;

            // Initialisation des services
            const scraper = new VintedScraper();
            const webhookManager = new WebhookManager();

            // Validation de l'URL du produit
            if (!scraper.isValidVintedUrl(productUrl)) {
                const errorEmbed = webhookManager.createErrorEmbed(
                    'URL invalide',
                    'L\'URL fournie n\'est pas un lien produit Vinted valide.\n\n**Format attendu:** `https://www.vinted.fr/items/123456789`'
                );
                return interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Validation de l'URL webhook
            if (!webhookManager.isValidWebhookUrl(webhookUrl)) {
                const errorEmbed = webhookManager.createErrorEmbed(
                    'Webhook invalide',
                    'L\'URL webhook fournie n\'est pas valide.\n\n**Format attendu:** `https://discord.com/api/webhooks/...`'
                );
                return interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Réponse immédiate pour éviter le timeout
            await interaction.deferReply();

            try {
                // Étape 1: Récupération des détails du produit
                await interaction.editReply('🔄 Analyse du produit Vinted en cours...');
                
                const productDetails = await scraper.getProductDetails(productUrl);
                
                if (!productDetails) {
                    throw new Error('Impossible de récupérer les détails du produit');
                }

                // Étape 2: Recherche de produits similaires
                await interaction.editReply('🔍 Recherche de produits similaires...');
                
                const similarProducts = await scraper.findSimilarProducts(productDetails, 10);

                // Étape 3: Sauvegarde de la configuration
                await interaction.editReply('💾 Sauvegarde de la configuration...');
                
                const saved = await webhookManager.saveWebhookForChannel(channelId, webhookUrl, productUrl);
                
                if (!saved) {
                    throw new Error('Erreur lors de la sauvegarde');
                }

                // Étape 4: Envoi via webhook
                await interaction.editReply('📤 Envoi des résultats...');

                if (similarProducts.length > 0) {
                    // Envoi du produit original + produits similaires
                    await webhookManager.sendSimilarProducts(webhookUrl, productDetails, similarProducts);
                } else {
                    // Envoi du produit original seulement
                    await webhookManager.sendProductToWebhook(webhookUrl, productDetails);
                }

                // Création de l'embed de succès
                const successEmbed = new EmbedBuilder()
                    .setColor('#00ff00')
                    .setTitle('✅ Configuration réussie')
                    .setDescription('Le lien produit a été analysé et configuré avec succès !')
                    .addFields(
                        {
                            name: '🎯 Produit analysé',
                            value: `[${productDetails.title}](${productDetails.url})`,
                            inline: false
                        },
                        {
                            name: '💰 Prix du produit',
                            value: `\`${productDetails.price}\``,
                            inline: true
                        },
                        {
                            name: '🏷️ Marque',
                            value: `\`${productDetails.brand_title}\``,
                            inline: true
                        },
                        {
                            name: '🔍 Produits similaires trouvés',
                            value: `\`${similarProducts.length}\` produit(s)`,
                            inline: true
                        },
                        {
                            name: '📍 Salon configuré',
                            value: `<#${channelId}>`,
                            inline: true
                        },
                        {
                            name: '🔗 Webhook',
                            value: '✅ Configuré',
                            inline: true
                        }
                    )
                    .setThumbnail(productDetails.photo?.url)
                    .setFooter({
                        text: 'Les résultats ont été envoyés via webhook'
                    })
                    .setTimestamp();

                if (similarProducts.length > 0) {
                    const cheapestProduct = similarProducts[0];
                    successEmbed.addFields({
                        name: '💸 Meilleur prix trouvé',
                        value: `[${cheapestProduct.price} - ${cheapestProduct.title.substring(0, 30)}...](${cheapestProduct.url})`,
                        inline: false
                    });
                }

                await interaction.editReply({ content: '', embeds: [successEmbed] });

            } catch (scrapingError) {
                console.error('Erreur lors du scraping:', scrapingError);
                
                const errorEmbed = webhookManager.createErrorEmbed(
                    'Erreur d\'analyse',
                    `Impossible d'analyser le produit Vinted.\n\n**Erreur:** ${scrapingError.message}\n\n**Suggestions:**\n• Vérifiez que l'URL est accessible\n• Réessayez dans quelques minutes\n• Assurez-vous que le produit existe toujours`
                );
                
                await interaction.editReply({ content: '', embeds: [errorEmbed] });
            }

        } catch (error) {
            console.error('Erreur dans la commande addlien:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Erreur interne')
                .setDescription('Une erreur inattendue s\'est produite. Veuillez réessayer plus tard.')
                .addFields({
                    name: 'Détails de l\'erreur',
                    value: `\`${error.message}\``,
                    inline: false
                })
                .setTimestamp();
            
            if (interaction.deferred) {
                await interaction.editReply({ content: '', embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
