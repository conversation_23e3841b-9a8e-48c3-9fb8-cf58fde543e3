# 🌐 Guide Multi-Guild - Bot Discord Vinted

## 🎯 Vue d'ensemble

Le Bot Discord Vinted est **nativement conçu pour fonctionner sur plusieurs serveurs Discord (guilds) simultanément**. Une seule instance du bot peut gérer des dizaines ou centaines de serveurs sans problème.

## ✅ Fonctionnalités Multi-Guild

### 🏗️ Architecture Distribuée
- **Une instance unique** du bot pour tous vos serveurs
- **Stockage centralisé** avec isolation des données par salon
- **Gestion indépendante** des liens Vinted par serveur
- **Scalabilité illimitée** - aucune limite sur le nombre de serveurs

### 🔧 Gestion Automatique
- **Détection automatique** des nouveaux serveurs
- **Logs détaillés** par serveur et salon
- **Statistiques globales** avec `!stats`
- **Isolation complète** des configurations

## 🚀 Configuration Multi-Guild

### 1. Invitation du Bot

Le bot utilise le **même lien d'invitation** pour tous les serveurs :

```
https://discord.com/api/oauth2/authorize?client_id=VOTRE_CLIENT_ID&permissions=2147483648&scope=bot
```

### 2. Permissions Requises

Sur chaque serveur, le bot a besoin de :
- ✅ **Envoyer des messages**
- ✅ **Utiliser les commandes slash**
- ✅ **Lire l'historique des messages**
- ✅ **Intégrer des liens**
- ✅ **Gérer les messages** (pour les commandes admin)

### 3. Configuration par Serveur

Chaque serveur peut avoir ses propres liens Vinted :

```bash
# Serveur Gaming
!addLien https://www.vinted.fr/vetements?search_text=gaming

# Serveur Mode
!addLien https://www.vinted.fr/vetements?search_text=designer

# Serveur Étudiants
!addLien https://www.vinted.fr/vetements?price_to=20
```

## 📊 Monitoring Multi-Guild

### Commande !stats

La commande `!stats` affiche :
- **Nombre total de serveurs** où le bot est présent
- **Nombre de liens configurés** par serveur
- **Statistiques globales** d'utilisation
- **Détails par serveur** (nom, canaux, liens)

### Logs Détaillés

Le bot génère des logs spécifiques au multi-guild :

```
🤖 Bot Discord Vinted démarré!
📝 Connecté en tant que: VintedBot#1234
🏠 Présent sur 5 serveur(s)
👥 1,247 utilisateurs accessibles

📋 Liste des serveurs:
   1. Gaming Community (156 membres)
   2. Fashion Hub (89 membres)
   3. Étudiants ESGI (234 membres)
   4. Sneakers Addicts (67 membres)
   5. Vintage Lovers (45 membres)

🔧 Commande exécutée: addLien par User#1234 dans #vinted-deals (Gaming Community)
🎉 Bot ajouté au serveur: New Fashion Server (23 membres)
📊 Total serveurs: 6
```

## 🔍 Exemples d'Utilisation

### Scénario 1: Serveurs Thématiques

```
🎮 Gaming Community
├── #vinted-gaming → Liens gaming/esport
└── #sneakers → Liens chaussures sport

👗 Fashion Hub
├── #designer → Liens marques de luxe
├── #vintage → Liens vintage/rétro
└── #accessories → Liens accessoires

🎓 Étudiants
└── #bon-plans → Liens < 20€
```

### Scénario 2: Serveurs Géographiques

```
🇫🇷 France Community
├── #vinted-fr → https://www.vinted.fr/...
└── #mode-paris → Liens spécifiques Paris

🇩🇪 Deutschland Server
├── #vinted-de → https://www.vinted.de/...
└── #berlin-style → Liens spécifiques Berlin

🇮🇹 Italia Community
└── #vinted-it → https://www.vinted.it/...
```

## 🛠️ Gestion Avancée

### Ajout d'un Nouveau Serveur

1. **Invitez le bot** avec le lien d'invitation
2. **Configurez les permissions** nécessaires
3. **Ajoutez des liens** avec `!addLien`
4. **Vérifiez le statut** avec `!stats`

### Suppression d'un Serveur

Quand le bot est retiré d'un serveur :
- **Logs automatiques** de la déconnexion
- **Données conservées** (au cas où le bot revient)
- **Aucun impact** sur les autres serveurs

### Maintenance

- **Redémarrage unique** affecte tous les serveurs
- **Mise à jour centralisée** du code
- **Sauvegarde unique** du fichier JSON
- **Monitoring global** depuis une seule instance

## 📈 Performances Multi-Guild

### Optimisations Intégrées

- **Gestion asynchrone** des événements
- **Cache Discord.js** pour les performances
- **Stockage JSON optimisé** par Channel ID
- **Logs non-bloquants** pour le debugging

### Limites Théoriques

- **Serveurs Discord** : Illimité (limité par Discord)
- **Liens par serveur** : Illimité
- **Canaux par serveur** : Illimité
- **Utilisateurs** : Limité par Discord (millions)

## 🔒 Sécurité Multi-Guild

### Isolation des Données

- **Channel ID unique** comme clé de stockage
- **Aucun partage** de données entre serveurs
- **Permissions vérifiées** par serveur
- **Logs séparés** par guild

### Gestion des Permissions

- **Vérification par serveur** des permissions utilisateur
- **Isolation des commandes** par guild
- **Respect des rôles** Discord par serveur

## 🆘 Dépannage Multi-Guild

### Problèmes Courants

**Le bot ne répond pas sur un serveur :**
- ✅ Vérifiez les permissions du bot
- ✅ Vérifiez que le bot est en ligne
- ✅ Testez `!stats` pour voir si le serveur est détecté

**Liens non sauvegardés :**
- ✅ Vérifiez les permissions "Gérer les messages"
- ✅ Vérifiez l'URL Vinted
- ✅ Consultez les logs du bot

**Statistiques incorrectes :**
- ✅ Redémarrez le bot pour actualiser le cache
- ✅ Vérifiez le fichier `data/links.json`

### Support

Pour obtenir de l'aide :
1. **Consultez les logs** du bot
2. **Utilisez `!stats`** pour diagnostiquer
3. **Vérifiez les permissions** sur chaque serveur
4. **Testez sur un serveur de test** d'abord

---

## 🎉 Conclusion

Le Bot Discord Vinted est **parfaitement adapté au multi-guild** et peut gérer efficacement plusieurs serveurs Discord simultanément. Son architecture centralisée mais isolée garantit des performances optimales et une sécurité maximale.

**Une seule instance = Tous vos serveurs couverts !** 🚀
