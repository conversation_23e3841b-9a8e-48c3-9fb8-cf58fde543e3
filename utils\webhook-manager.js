const { WebhookClient, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const Storage = require('./storage');

class WebhookManager {
    constructor() {
        this.storage = new Storage();
    }

    /**
     * Crée un embed pour un produit Vinted
     */
    createProductEmbed(annonce) {
        try {
            const embed = new EmbedBuilder()
                .setColor(annonce.photo?.dominant_color || '#1abc9c')
                .setAuthor({ 
                    name: annonce.user?.login || 'Utilisateur Vinted', 
                    iconURL: annonce.user?.photo?.url || null, 
                    url: annonce.user?.profile_url || null 
                })
                .setTitle(annonce.title || 'Produit Vinted')
                .setURL(annonce.url)
                .setImage(annonce.photo?.url || null)
                .addFields(
                    { name: '`💸` Prix', value: `\`${annonce.price || 'N/A'}\``, inline: true },
                    { name: '`🏷️` Marque', value: `\`${annonce.brand_title || 'N/A'}\``, inline: true },
                    { name: '`📏` Taille', value: `\`${annonce.size_title || 'N/A'}\``, inline: true },
                    { name: '`📆` Date du post', value: `<t:${annonce.photo?.high_resolution?.timestamp || Math.floor(Date.now() / 1000)}:R>`, inline: true },
                )
                .setTimestamp()
                .setFooter({ 
                    text: 'Vinted Finders', 
                    iconURL: 'https://cdn.discordapp.com/attachments/1089538587072151664/1100784979673354310/Logo_lsv1.png' 
                });

            return embed;
        } catch (error) {
            console.error('Erreur lors de la création de l\'embed:', error);
            return new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Erreur')
                .setDescription('Impossible de créer l\'embed pour ce produit')
                .setTimestamp();
        }
    }

    /**
     * Crée les boutons d'action pour un produit
     */
    createActionButtons(annonce) {
        try {
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setEmoji('➕')
                        .setLabel("Plus d'info")
                        .setURL(annonce.url)
                        .setStyle(ButtonStyle.Link)
                )
                .addComponents(
                    new ButtonBuilder()
                        .setEmoji('💬')
                        .setLabel("Envoyer un message")
                        .setURL(`https://www.vinted.fr/items/${annonce.id}/want_it/new?button_name=receiver_id=${annonce.id}`)
                        .setStyle(ButtonStyle.Link)
                )
                .addComponents(
                    new ButtonBuilder()
                        .setEmoji('💸')
                        .setLabel("Acheter")
                        .setURL(`https://www.vinted.fr/transaction/buy/new?source_screen=item&transaction%5Bitem_id%5D=${annonce.id}`)
                        .setStyle(ButtonStyle.Link)
                );

            return row;
        } catch (error) {
            console.error('Erreur lors de la création des boutons:', error);
            return null;
        }
    }

    /**
     * Envoie un produit via webhook
     */
    async sendProductToWebhook(webhookUrl, annonce) {
        try {
            if (!webhookUrl || !annonce) {
                throw new Error('URL webhook ou données produit manquantes');
            }

            const webhookClient = new WebhookClient({ url: webhookUrl });
            const embed = this.createProductEmbed(annonce);
            const buttons = this.createActionButtons(annonce);

            const messageOptions = { embeds: [embed] };
            if (buttons) {
                messageOptions.components = [buttons];
            }

            await webhookClient.send(messageOptions);
            return true;

        } catch (error) {
            console.error('Erreur lors de l\'envoi via webhook:', error);
            return false;
        }
    }

    /**
     * Envoie plusieurs produits similaires
     */
    async sendSimilarProducts(webhookUrl, originalProduct, similarProducts) {
        try {
            if (!webhookUrl || !originalProduct || !similarProducts.length) {
                return false;
            }

            const webhookClient = new WebhookClient({ url: webhookUrl });

            // Embed principal pour le produit original
            const originalEmbed = this.createProductEmbed(originalProduct);
            originalEmbed.setTitle(`🎯 Produit recherché: ${originalProduct.title}`);

            // Embed pour les produits similaires
            const similarEmbed = new EmbedBuilder()
                .setColor('#3498db')
                .setTitle('🔍 Produits similaires trouvés (triés par prix)')
                .setDescription(`${similarProducts.length} produit(s) similaire(s) au prix le plus bas`)
                .setTimestamp();

            // Ajout des produits similaires comme fields
            similarProducts.slice(0, 10).forEach((product, index) => {
                similarEmbed.addFields({
                    name: `${index + 1}. ${product.price}`,
                    value: `[${product.title.substring(0, 50)}...](${product.url})`,
                    inline: false
                });
            });

            await webhookClient.send({ 
                embeds: [originalEmbed, similarEmbed]
            });

            return true;

        } catch (error) {
            console.error('Erreur lors de l\'envoi des produits similaires:', error);
            return false;
        }
    }

    /**
     * Récupère l'URL webhook pour un salon
     */
    async getWebhookForChannel(channelId) {
        try {
            const linkData = await this.storage.getLink(channelId);
            return linkData?.webhook || null;
        } catch (error) {
            console.error('Erreur lors de la récupération du webhook:', error);
            return null;
        }
    }

    /**
     * Sauvegarde l'URL webhook pour un salon
     */
    async saveWebhookForChannel(channelId, webhookUrl, productUrl) {
        try {
            const data = await this.storage.loadData();
            data[channelId] = {
                webhook: webhookUrl,
                productUrl: productUrl,
                addedAt: new Date().toISOString(),
                lastChecked: null
            };
            return await this.storage.saveData(data);
        } catch (error) {
            console.error('Erreur lors de la sauvegarde du webhook:', error);
            return false;
        }
    }

    /**
     * Valide une URL webhook Discord
     */
    isValidWebhookUrl(url) {
        const webhookRegex = /^https:\/\/discord\.com\/api\/webhooks\/\d+\/[\w-]+$/;
        return webhookRegex.test(url);
    }

    /**
     * Crée un embed d'erreur
     */
    createErrorEmbed(title, description) {
        return new EmbedBuilder()
            .setColor('#ff0000')
            .setTitle(`❌ ${title}`)
            .setDescription(description)
            .setTimestamp();
    }

    /**
     * Crée un embed de succès
     */
    createSuccessEmbed(title, description) {
        return new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle(`✅ ${title}`)
            .setDescription(description)
            .setTimestamp();
    }
}

module.exports = WebhookManager;
