const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const VintedScraper = require('../../utils/vinted-scraper');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('recherche')
        .setDescription('Recherche les meilleurs prix sur Vinted')
        .addStringOption(option =>
            option.setName('termes')
                .setDescription('Mots-clés de recherche (ex: "nike t-shirt")')
                .setRequired(true))
        .addIntegerOption(option =>
            option.setName('prix_max')
                .setDescription('Prix maximum en euros')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(1000))
        .addStringOption(option =>
            option.setName('taille')
                .setDescription('Taille recherchée')
                .setRequired(false)
                .addChoices(
                    { name: 'XS', value: 'XS' },
                    { name: 'S', value: 'S' },
                    { name: 'M', value: 'M' },
                    { name: 'L', value: 'L' },
                    { name: 'XL', value: 'XL' },
                    { name: 'XXL', value: 'XXL' },
                    { name: '36', value: '36' },
                    { name: '37', value: '37' },
                    { name: '38', value: '38' },
                    { name: '39', value: '39' },
                    { name: '40', value: '40' },
                    { name: '41', value: '41' },
                    { name: '42', value: '42' },
                    { name: '43', value: '43' },
                    { name: '44', value: '44' },
                    { name: '45', value: '45' }
                ))
        .addStringOption(option =>
            option.setName('categorie')
                .setDescription('Catégorie de produits')
                .setRequired(false)
                .addChoices(
                    { name: 'Vêtements', value: 'vetements' },
                    { name: 'Chaussures', value: 'chaussures' },
                    { name: 'Sacs', value: 'sacs' },
                    { name: 'Accessoires', value: 'accessoires' }
                )),

    async execute(interaction) {
        try {
            const searchTerms = interaction.options.getString('termes');
            const maxPrice = interaction.options.getInteger('prix_max');
            const size = interaction.options.getString('taille');
            const category = interaction.options.getString('categorie') || 'vetements';

            // Validation des termes de recherche
            if (searchTerms.length < 2) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff0000')
                    .setTitle('❌ Recherche invalide')
                    .setDescription('Les termes de recherche doivent contenir au moins 2 caractères.')
                    .setTimestamp();
                
                return interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Réponse immédiate pour éviter le timeout
            await interaction.deferReply();

            const scraper = new VintedScraper();

            try {
                // Message de progression
                await interaction.editReply('🔍 Recherche en cours sur Vinted...');

                // Recherche des produits
                const searchOptions = {
                    maxResults: 15,
                    maxPrice: maxPrice,
                    size: size,
                    category: category
                };

                const products = await scraper.searchProducts(searchTerms, searchOptions);

                if (products.length === 0) {
                    const noResultEmbed = new EmbedBuilder()
                        .setColor('#ffaa00')
                        .setTitle('🔍 Aucun résultat')
                        .setDescription(`Aucun produit trouvé pour "${searchTerms}"`)
                        .addFields({
                            name: '💡 Suggestions',
                            value: '• Essayez des mots-clés plus généraux\n• Vérifiez l\'orthographe\n• Augmentez le prix maximum\n• Changez de catégorie',
                            inline: false
                        })
                        .setTimestamp();

                    return interaction.editReply({ content: '', embeds: [noResultEmbed] });
                }

                // Création de l'embed principal
                const mainEmbed = new EmbedBuilder()
                    .setColor('#1abc9c')
                    .setTitle(`🔍 Recherche: ${searchTerms}`)
                    .setDescription(`**${products.length} produit(s) trouvé(s)** - Triés par prix croissant`)
                    .setTimestamp();

                // Ajout des filtres appliqués
                let filtersText = '';
                if (maxPrice) filtersText += `💰 Prix max: ${maxPrice}€\n`;
                if (size) filtersText += `📏 Taille: ${size}\n`;
                if (category !== 'vetements') filtersText += `📂 Catégorie: ${category}\n`;

                if (filtersText) {
                    mainEmbed.addFields({
                        name: '🔧 Filtres appliqués',
                        value: filtersText,
                        inline: true
                    });
                }

                // Affichage des 5 meilleurs résultats
                const topProducts = products.slice(0, 5);
                let resultsText = '';

                topProducts.forEach((product, index) => {
                    const emoji = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏷️';
                    resultsText += `${emoji} **${product.price}** - [${product.title}](${product.url})\n`;
                    resultsText += `   📦 ${product.brand} • 📏 ${product.size}\n\n`;
                });

                mainEmbed.addFields({
                    name: '🏆 Meilleurs prix trouvés',
                    value: resultsText,
                    inline: false
                });

                // Statistiques
                const avgPrice = products.reduce((sum, p) => sum + p.priceValue, 0) / products.length;
                const minPrice = products[0].priceValue;
                const maxPriceFound = products[products.length - 1].priceValue;

                mainEmbed.addFields({
                    name: '📊 Statistiques',
                    value: `💸 **Prix le plus bas:** ${minPrice.toFixed(2)}€\n💰 **Prix moyen:** ${avgPrice.toFixed(2)}€\n📈 **Prix le plus haut:** ${maxPriceFound.toFixed(2)}€`,
                    inline: true
                });

                // Création des boutons pour les 3 meilleurs produits
                const row = new ActionRowBuilder();
                
                topProducts.slice(0, 3).forEach((product, index) => {
                    const emoji = index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉';
                    row.addComponents(
                        new ButtonBuilder()
                            .setEmoji(emoji)
                            .setLabel(`${product.price}`)
                            .setURL(product.url)
                            .setStyle(ButtonStyle.Link)
                    );
                });

                // Bouton pour voir plus de résultats
                if (products.length > 5) {
                    const moreResultsEmbed = new EmbedBuilder()
                        .setColor('#3498db')
                        .setTitle('📋 Autres résultats')
                        .setDescription(`${products.length - 5} autre(s) produit(s) trouvé(s)`)
                        .setTimestamp();

                    let moreResultsText = '';
                    products.slice(5, 10).forEach((product, index) => {
                        moreResultsText += `${index + 6}. **${product.price}** - [${product.title.substring(0, 50)}...](${product.url})\n`;
                    });

                    if (moreResultsText) {
                        moreResultsEmbed.addFields({
                            name: '🔍 Résultats supplémentaires',
                            value: moreResultsText,
                            inline: false
                        });
                    }

                    await interaction.editReply({ 
                        content: '', 
                        embeds: [mainEmbed, moreResultsEmbed], 
                        components: [row] 
                    });
                } else {
                    await interaction.editReply({ 
                        content: '', 
                        embeds: [mainEmbed], 
                        components: [row] 
                    });
                }

            } catch (scrapingError) {
                console.error('Erreur lors de la recherche:', scrapingError);
                
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff0000')
                    .setTitle('❌ Erreur de recherche')
                    .setDescription(`Impossible d'effectuer la recherche sur Vinted.`)
                    .addFields(
                        {
                            name: 'Erreur',
                            value: `\`${scrapingError.message}\``,
                            inline: false
                        },
                        {
                            name: '💡 Suggestions',
                            value: '• Réessayez dans quelques minutes\n• Vérifiez votre connexion internet\n• Essayez des termes différents',
                            inline: false
                        }
                    )
                    .setTimestamp();
                
                await interaction.editReply({ content: '', embeds: [errorEmbed] });
            }

        } catch (error) {
            console.error('Erreur dans la commande recherche:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Erreur interne')
                .setDescription('Une erreur inattendue s\'est produite.')
                .addFields({
                    name: 'Détails',
                    value: `\`${error.message}\``,
                    inline: false
                })
                .setTimestamp();
            
            if (interaction.deferred) {
                await interaction.editReply({ content: '', embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
