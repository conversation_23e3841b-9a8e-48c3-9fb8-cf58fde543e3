const axios = require('axios');
const cheerio = require('cheerio');

class VintedAPI {
    constructor() {
        this.baseHeaders = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        };
    }

    /**
     * Valide si une URL est bien une URL Vinted valide
     * @param {string} url - URL à valider
     * @returns {boolean} True si l'URL est valide
     */
    isValidVintedUrl(url) {
        const vintedRegex = /^https?:\/\/(www\.)?vinted\.(fr|com|de|it|es|pl|lt|cz|sk|be|nl|at|lu)\//;
        return vintedRegex.test(url);
    }

    /**
     * Extrait les informations d'une recherche Vinted
     * @param {string} url - URL de recherche Vinted
     * @returns {Promise<Object>} Informations extraites
     */
    async extractSearchInfo(url) {
        try {
            if (!this.isValidVintedUrl(url)) {
                throw new Error('URL Vinted invalide');
            }

            const response = await axios.get(url, {
                headers: this.baseHeaders,
                timeout: 10000
            });

            const $ = cheerio.load(response.data);
            
            // Extraction du titre de la page
            const title = $('title').text().trim() || 'Recherche Vinted';
            
            // Tentative d'extraction du nombre de résultats
            let resultCount = 'Non déterminé';
            const resultText = $('.web_ui__Text').text();
            const countMatch = resultText.match(/(\d+)\s*résultats?/i);
            if (countMatch) {
                resultCount = countMatch[1];
            }

            // Extraction des paramètres de recherche depuis l'URL
            const urlParams = new URL(url);
            const searchParams = {};
            
            // Paramètres courants de Vinted
            const paramMapping = {
                'search_text': 'Recherche',
                'brand_ids[]': 'Marques',
                'size_ids[]': 'Tailles',
                'color_ids[]': 'Couleurs',
                'price_from': 'Prix min',
                'price_to': 'Prix max',
                'category_ids[]': 'Catégories'
            };

            for (const [param, label] of Object.entries(paramMapping)) {
                const value = urlParams.searchParams.get(param);
                if (value) {
                    searchParams[label] = value;
                }
            }

            return {
                success: true,
                title: title,
                url: url,
                resultCount: resultCount,
                searchParams: searchParams,
                extractedAt: new Date().toISOString()
            };

        } catch (error) {
            console.error('Erreur lors de l\'extraction des informations Vinted:', error.message);
            return {
                success: false,
                error: error.message,
                url: url
            };
        }
    }

    /**
     * Vérifie si une URL Vinted est accessible
     * @param {string} url - URL à vérifier
     * @returns {Promise<boolean>} True si accessible
     */
    async checkUrlAccessibility(url) {
        try {
            const response = await axios.head(url, {
                headers: this.baseHeaders,
                timeout: 5000
            });
            return response.status === 200;
        } catch (error) {
            console.error('URL non accessible:', error.message);
            return false;
        }
    }

    /**
     * Formate les informations pour l'affichage Discord
     * @param {Object} info - Informations extraites
     * @returns {Object} Embed Discord formaté
     */
    formatForDiscord(info) {
        if (!info.success) {
            return {
                color: 0xff0000,
                title: '❌ Erreur',
                description: `Impossible d'analyser le lien Vinted: ${info.error}`,
                timestamp: new Date()
            };
        }

        const fields = [];
        
        if (info.resultCount !== 'Non déterminé') {
            fields.push({
                name: '📊 Résultats',
                value: info.resultCount,
                inline: true
            });
        }

        if (Object.keys(info.searchParams).length > 0) {
            const paramsText = Object.entries(info.searchParams)
                .map(([key, value]) => `**${key}:** ${value}`)
                .join('\n');
            
            fields.push({
                name: '🔍 Paramètres de recherche',
                value: paramsText,
                inline: false
            });
        }

        return {
            color: 0x00ff00,
            title: '✅ Lien Vinted ajouté',
            description: info.title,
            url: info.url,
            fields: fields,
            footer: {
                text: 'Vinted Bot'
            },
            timestamp: new Date()
        };
    }
}

module.exports = VintedAPI;
