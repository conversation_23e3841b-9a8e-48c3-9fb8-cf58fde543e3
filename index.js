const { Client, GatewayIntentBits, Collection } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('./config.json');

// Création du client Discord avec les intents nécessaires
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
    ]
});

// Collection pour stocker les commandes slash
client.commands = new Collection();

// Chargement des commandes slash
const foldersPath = path.join(__dirname, 'commands');
const commandFolders = fs.readdirSync(foldersPath);

for (const folder of commandFolders) {
    const commandsPath = path.join(foldersPath, folder);
    const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);

        if ('data' in command && 'execute' in command) {
            client.commands.set(command.data.name, command);
            console.log(`✅ Slash command chargée: /${command.data.name}`);
        } else {
            console.log(`⚠️ Commande ignorée (structure invalide): ${file}`);
        }
    }
}

// Événement: Bot prêt
client.once('ready', () => {
    console.log('🤖 Bot Discord Vinted démarré!');
    console.log(`📝 Connecté en tant que: ${client.user.tag}`);
    console.log(`🏠 Présent sur ${client.guilds.cache.size} serveur(s)`);
    console.log(`👥 ${client.users.cache.size} utilisateurs accessibles`);

    // Affichage détaillé des serveurs
    if (client.guilds.cache.size > 0) {
        console.log('\n📋 Liste des serveurs:');
        client.guilds.cache.forEach((guild, index) => {
            console.log(`   ${index + 1}. ${guild.name} (${guild.memberCount} membres)`);
        });
    }

    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // Définition du statut du bot
    client.user.setActivity('Vinted | !addLien', { type: 3 }); // 3 = WATCHING
});

// Événement: Interaction (Slash Commands)
client.on('interactionCreate', async (interaction) => {
    if (!interaction.isChatInputCommand()) return;

    const command = client.commands.get(interaction.commandName);

    if (!command) {
        console.error(`❌ Aucune commande correspondant à ${interaction.commandName} n'a été trouvée.`);
        return;
    }

    try {
        // Log de l'exécution de la commande
        const guildName = interaction.guild ? interaction.guild.name : 'DM';
        console.log(`🔧 Slash command exécutée: /${interaction.commandName} par ${interaction.user.tag} dans #${interaction.channel.name} (${guildName})`);

        await command.execute(interaction);

    } catch (error) {
        console.error(`❌ Erreur lors de l'exécution de /${interaction.commandName}:`, error);

        const errorMessage = {
            content: '❌ Une erreur est survenue lors de l\'exécution de cette commande !',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
});

// Événement: Bot rejoint un serveur
client.on('guildCreate', (guild) => {
    console.log(`🎉 Bot ajouté au serveur: ${guild.name} (${guild.memberCount} membres)`);
    console.log(`📊 Total serveurs: ${client.guilds.cache.size}`);
});

// Événement: Bot quitte un serveur
client.on('guildDelete', (guild) => {
    console.log(`👋 Bot retiré du serveur: ${guild.name}`);
    console.log(`📊 Total serveurs: ${client.guilds.cache.size}`);
});

// Événement: Erreur
client.on('error', (error) => {
    console.error('❌ Erreur Discord.js:', error);
});

// Événement: Avertissement
client.on('warn', (warning) => {
    console.warn('⚠️ Avertissement Discord.js:', warning);
});

// Gestion des erreurs non capturées
process.on('unhandledRejection', (error) => {
    console.error('❌ Erreur non gérée (Promise):', error);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Exception non capturée:', error);
    process.exit(1);
});

// Connexion du bot
if (!config.token || config.token === 'VOTRE_TOKEN_DISCORD_ICI') {
    console.error('❌ Token Discord manquant!');
    console.log('📝 Veuillez configurer votre token dans config.json');
    process.exit(1);
}

client.login(config.token);
