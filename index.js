const { Client, GatewayIntentBits, Collection, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('./config.json');

// Création du client Discord avec les intents nécessaires
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
    ]
});

// Collection pour stocker les commandes
client.commands = new Collection();

// Chargement des commandes
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

for (const file of commandFiles) {
    const filePath = path.join(commandsPath, file);
    const command = require(filePath);
    
    if ('name' in command && 'execute' in command) {
        client.commands.set(command.name, command);
        console.log(`✅ Commande chargée: ${command.name}`);
    } else {
        console.log(`⚠️ Commande ignorée (structure invalide): ${file}`);
    }
}

// Événement: Bot prêt
client.once('ready', () => {
    console.log('🤖 Bot Discord Vinted démarré!');
    console.log(`📝 Connecté en tant que: ${client.user.tag}`);
    console.log(`🏠 Présent sur ${client.guilds.cache.size} serveur(s)`);
    console.log(`👥 ${client.users.cache.size} utilisateurs accessibles`);

    // Affichage détaillé des serveurs
    if (client.guilds.cache.size > 0) {
        console.log('\n📋 Liste des serveurs:');
        client.guilds.cache.forEach((guild, index) => {
            console.log(`   ${index + 1}. ${guild.name} (${guild.memberCount} membres)`);
        });
    }

    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // Définition du statut du bot
    client.user.setActivity('Vinted | !addLien', { type: 3 }); // 3 = WATCHING
});

// Événement: Nouveau message
client.on('messageCreate', async (message) => {
    // Ignorer les messages des bots
    if (message.author.bot) return;
    
    // Vérifier si le message commence par le préfixe
    if (!message.content.startsWith(config.prefix)) return;
    
    // Extraire la commande et les arguments
    const args = message.content.slice(config.prefix.length).trim().split(/ +/);
    const commandName = args.shift().toLowerCase();
    
    // Rechercher la commande
    const command = client.commands.get(commandName);
    
    if (!command) {
        // Commande non trouvée - afficher l'aide
        const helpEmbed = new EmbedBuilder()
            .setColor(0x0099ff)
            .setTitle('🤖 Bot Discord Vinted')
            .setDescription('Commandes disponibles:')
            .addFields(
                {
                    name: '📎 !addLien <url>',
                    value: 'Ajoute un lien Vinted à surveiller pour ce salon',
                    inline: false
                },
                {
                    name: '🗑️ !removeLien',
                    value: 'Supprime le lien Vinted configuré pour ce salon',
                    inline: false
                }
            )
            .setFooter({
                text: 'Vinted Bot - Surveillance de liens'
            })
            .setTimestamp();
        
        return message.reply({ embeds: [helpEmbed] });
    }
    
    try {
        // Exécution de la commande
        const guildName = message.guild ? message.guild.name : 'DM';
        console.log(`🔧 Commande exécutée: ${commandName} par ${message.author.tag} dans #${message.channel.name} (${guildName})`);
        await command.execute(message, args);
        
    } catch (error) {
        console.error(`❌ Erreur lors de l'exécution de la commande ${commandName}:`, error);
        
        const errorEmbed = new EmbedBuilder()
            .setColor(0xff0000)
            .setTitle('❌ Erreur d\'exécution')
            .setDescription('Une erreur est survenue lors de l\'exécution de la commande.')
            .addFields({
                name: 'Détails',
                value: 'Veuillez réessayer ou contacter un administrateur si le problème persiste.',
                inline: false
            })
            .setTimestamp();
        
        try {
            await message.reply({ embeds: [errorEmbed] });
        } catch (replyError) {
            console.error('❌ Impossible de répondre au message:', replyError);
        }
    }
});

// Événement: Bot rejoint un serveur
client.on('guildCreate', (guild) => {
    console.log(`🎉 Bot ajouté au serveur: ${guild.name} (${guild.memberCount} membres)`);
    console.log(`📊 Total serveurs: ${client.guilds.cache.size}`);
});

// Événement: Bot quitte un serveur
client.on('guildDelete', (guild) => {
    console.log(`👋 Bot retiré du serveur: ${guild.name}`);
    console.log(`📊 Total serveurs: ${client.guilds.cache.size}`);
});

// Événement: Erreur
client.on('error', (error) => {
    console.error('❌ Erreur Discord.js:', error);
});

// Événement: Avertissement
client.on('warn', (warning) => {
    console.warn('⚠️ Avertissement Discord.js:', warning);
});

// Gestion des erreurs non capturées
process.on('unhandledRejection', (error) => {
    console.error('❌ Erreur non gérée (Promise):', error);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Exception non capturée:', error);
    process.exit(1);
});

// Connexion du bot
if (!config.token || config.token === 'VOTRE_TOKEN_DISCORD_ICI') {
    console.error('❌ Token Discord manquant!');
    console.log('📝 Veuillez configurer votre token dans config.json');
    process.exit(1);
}

client.login(config.token);
