const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const Storage = require('../../utils/storage');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('removelien')
        .setDescription('Supprime la configuration Vinted pour ce salon'),

    async execute(interaction) {
        try {
            // Vérification des permissions
            if (!interaction.member.permissions.has('ManageMessages')) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff0000')
                    .setTitle('❌ Permission refusée')
                    .setDescription('Vous devez avoir la permission "Gérer les messages" pour utiliser cette commande.')
                    .setTimestamp();
                
                return interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const channelId = interaction.channel.id;
            const storage = new Storage();

            // Vérification si une configuration existe pour ce salon
            const existingConfig = await storage.getLink(channelId);
            
            if (!existingConfig) {
                const infoEmbed = new EmbedBuilder()
                    .setColor('#ffaa00')
                    .setTitle('ℹ️ Aucune configuration')
                    .setDescription('Aucune configuration Vinted n\'est active pour ce salon.')
                    .addFields({
                        name: 'Action suggérée',
                        value: 'Utilisez `/addlien` pour configurer un produit Vinted à surveiller.',
                        inline: false
                    })
                    .setTimestamp();
                
                return interaction.reply({ embeds: [infoEmbed], ephemeral: true });
            }

            // Réponse immédiate
            await interaction.deferReply();

            // Suppression de la configuration
            const removed = await storage.removeLink(channelId);
            
            if (!removed) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff0000')
                    .setTitle('❌ Erreur de suppression')
                    .setDescription('Une erreur est survenue lors de la suppression de la configuration.')
                    .setTimestamp();
                
                return interaction.editReply({ embeds: [errorEmbed] });
            }

            // Création de l'embed de succès
            const successEmbed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('✅ Configuration supprimée')
                .setDescription('La configuration Vinted a été supprimée avec succès de ce salon.')
                .addFields(
                    {
                        name: '🗑️ Produit supprimé',
                        value: existingConfig.productUrl || existingConfig.url || 'URL non disponible',
                        inline: false
                    },
                    {
                        name: '📍 Salon',
                        value: `<#${channelId}>`,
                        inline: true
                    },
                    {
                        name: '📅 Configuré le',
                        value: new Date(existingConfig.addedAt).toLocaleDateString('fr-FR', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                        }),
                        inline: true
                    },
                    {
                        name: '🔗 Webhook',
                        value: existingConfig.webhook ? '✅ Était configuré' : '❌ Non configuré',
                        inline: true
                    }
                )
                .setFooter({
                    text: 'Vinted Finders'
                })
                .setTimestamp();

            await interaction.editReply({ embeds: [successEmbed] });

        } catch (error) {
            console.error('Erreur dans la commande removelien:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Erreur interne')
                .setDescription('Une erreur inattendue s\'est produite. Veuillez réessayer plus tard.')
                .addFields({
                    name: 'Détails de l\'erreur',
                    value: `\`${error.message}\``,
                    inline: false
                })
                .setTimestamp();
            
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
