const axios = require('axios');
const cheerio = require('cheerio');

class VintedScraper {
    constructor() {
        this.baseHeaders = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        };
        this.cache = new Map();
        this.lastRequest = 0;
        this.minDelay = 1000; // 1 seconde entre les requêtes
    }

    /**
     * Ajoute un délai entre les requêtes pour éviter le rate limiting
     */
    async delay() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequest;
        if (timeSinceLastRequest < this.minDelay) {
            await new Promise(resolve => setTimeout(resolve, this.minDelay - timeSinceLastRequest));
        }
        this.lastRequest = Date.now();
    }

    /**
     * Valide si une URL est bien une URL Vinted valide
     */
    isValidVintedUrl(url) {
        const vintedRegex = /^https?:\/\/(www\.)?vinted\.(fr|com|de|it|es|pl|lt|cz|sk|be|nl|at|lu)\//;
        return vintedRegex.test(url);
    }

    /**
     * Extrait l'ID d'un produit depuis son URL
     */
    extractProductId(url) {
        const match = url.match(/\/items\/(\d+)/);
        return match ? match[1] : null;
    }

    /**
     * Récupère les détails d'un produit Vinted
     */
    async getProductDetails(productUrl) {
        try {
            if (!this.isValidVintedUrl(productUrl)) {
                throw new Error('URL Vinted invalide');
            }

            const productId = this.extractProductId(productUrl);
            if (!productId) {
                throw new Error('Impossible d\'extraire l\'ID du produit');
            }

            // Vérifier le cache
            if (this.cache.has(productId)) {
                const cached = this.cache.get(productId);
                if (Date.now() - cached.timestamp < 300000) { // 5 minutes
                    return cached.data;
                }
            }

            await this.delay();

            const response = await axios.get(productUrl, {
                headers: this.baseHeaders,
                timeout: 15000
            });

            const $ = cheerio.load(response.data);
            
            // Extraction des données du script JSON-LD ou des métadonnées
            let productData = null;
            
            // Tentative d'extraction depuis les scripts JSON
            $('script[type="application/ld+json"]').each((i, elem) => {
                try {
                    const jsonData = JSON.parse($(elem).html());
                    if (jsonData['@type'] === 'Product') {
                        productData = jsonData;
                    }
                } catch (e) {
                    // Ignore les erreurs de parsing JSON
                }
            });

            // Extraction manuelle si JSON-LD non disponible
            if (!productData) {
                productData = this.extractProductDataManually($, productUrl, productId);
            }

            // Mise en cache
            this.cache.set(productId, {
                data: productData,
                timestamp: Date.now()
            });

            return productData;

        } catch (error) {
            console.error('Erreur lors de la récupération du produit:', error.message);
            throw error;
        }
    }

    /**
     * Extraction manuelle des données produit
     */
    extractProductDataManually($, productUrl, productId) {
        const title = $('h1[data-testid="item-title"]').text().trim() || 
                     $('h1').first().text().trim() || 
                     $('title').text().replace(' | Vinted', '').trim();

        const price = $('[data-testid="item-price"]').text().trim() || 
                     $('.item-price').text().trim() || 
                     $('[class*="price"]').first().text().trim();

        const brand = $('[data-testid="item-brand"]').text().trim() || 
                     $('.item-brand').text().trim() ||
                     $('[class*="brand"]').first().text().trim();

        const size = $('[data-testid="item-size"]').text().trim() || 
                    $('.item-size').text().trim() ||
                    $('[class*="size"]').first().text().trim();

        const imageUrl = $('img[data-testid="item-photo"]').attr('src') || 
                        $('.item-photo img').attr('src') ||
                        $('img').first().attr('src');

        // Extraction des informations utilisateur
        const userLogin = $('[data-testid="user-login"]').text().trim() ||
                         $('.user-login').text().trim() ||
                         $('[class*="user"]').first().text().trim();

        const userPhotoUrl = $('[data-testid="user-photo"]').attr('src') ||
                            $('.user-photo img').attr('src');

        const userProfileUrl = $('[data-testid="user-profile"]').attr('href') ||
                              $('.user-profile').attr('href');

        // Tentative d'extraction de la couleur dominante (approximation)
        const dominantColor = this.extractDominantColor($) || '#1abc9c';

        return {
            id: productId,
            title: title,
            url: productUrl,
            price: price,
            brand_title: brand,
            size_title: size,
            photo: {
                url: imageUrl,
                dominant_color: dominantColor,
                high_resolution: {
                    timestamp: Math.floor(Date.now() / 1000)
                }
            },
            user: {
                login: userLogin,
                photo: {
                    url: userPhotoUrl
                },
                profile_url: userProfileUrl
            }
        };
    }

    /**
     * Extrait une couleur dominante approximative
     */
    extractDominantColor($) {
        // Couleurs par défaut basées sur des mots-clés
        const colorKeywords = {
            'rouge': '#e74c3c',
            'red': '#e74c3c',
            'bleu': '#3498db',
            'blue': '#3498db',
            'vert': '#2ecc71',
            'green': '#2ecc71',
            'noir': '#2c3e50',
            'black': '#2c3e50',
            'blanc': '#ecf0f1',
            'white': '#ecf0f1',
            'rose': '#e91e63',
            'pink': '#e91e63',
            'jaune': '#f1c40f',
            'yellow': '#f1c40f'
        };

        const text = $('body').text().toLowerCase();
        for (const [keyword, color] of Object.entries(colorKeywords)) {
            if (text.includes(keyword)) {
                return color;
            }
        }

        return '#1abc9c'; // Couleur par défaut
    }

    /**
     * Recherche des produits similaires
     */
    async findSimilarProducts(originalProduct, maxResults = 10) {
        try {
            if (!originalProduct.title || !originalProduct.brand_title) {
                throw new Error('Données produit insuffisantes pour la recherche');
            }

            // Construction de la requête de recherche
            const searchTerms = [
                originalProduct.brand_title,
                ...originalProduct.title.split(' ').slice(0, 3) // Premiers mots du titre
            ].filter(term => term && term.length > 2);

            const searchQuery = searchTerms.join(' ');
            const searchUrl = `https://www.vinted.fr/vetements?search_text=${encodeURIComponent(searchQuery)}`;

            await this.delay();

            const response = await axios.get(searchUrl, {
                headers: this.baseHeaders,
                timeout: 15000
            });

            const $ = cheerio.load(response.data);
            const products = [];

            // Extraction des produits de la page de recherche
            $('.feed-grid__item, .item-box, [data-testid="item-box"]').each((i, elem) => {
                if (products.length >= maxResults) return false;

                const $item = $(elem);
                const itemUrl = $item.find('a').attr('href');
                const itemPrice = $item.find('[class*="price"]').text().trim();
                const itemTitle = $item.find('[class*="title"]').text().trim();

                if (itemUrl && itemPrice && itemTitle) {
                    const fullUrl = itemUrl.startsWith('http') ? itemUrl : `https://www.vinted.fr${itemUrl}`;
                    const productId = this.extractProductId(fullUrl);

                    if (productId && productId !== originalProduct.id) {
                        products.push({
                            id: productId,
                            url: fullUrl,
                            title: itemTitle,
                            price: itemPrice,
                            priceValue: this.extractPriceValue(itemPrice)
                        });
                    }
                }
            });

            // Tri par prix croissant
            products.sort((a, b) => a.priceValue - b.priceValue);

            return products.slice(0, maxResults);

        } catch (error) {
            console.error('Erreur lors de la recherche de produits similaires:', error.message);
            return [];
        }
    }

    /**
     * Extrait la valeur numérique du prix
     */
    extractPriceValue(priceString) {
        const match = priceString.match(/(\d+(?:[.,]\d+)?)/);
        return match ? parseFloat(match[1].replace(',', '.')) : 999999;
    }

    /**
     * Nettoie le cache
     */
    clearCache() {
        this.cache.clear();
    }
}

module.exports = VintedScraper;
