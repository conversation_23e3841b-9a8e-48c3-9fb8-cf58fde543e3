# 🚀 Guide de Configuration Rapide

## 📋 Étapes de Configuration

### 1. 🤖 Créer votre Bot Discord

1. **Allez sur le Discord Developer Portal**
   - Visitez : https://discord.com/developers/applications
   - Connectez-vous avec votre compte Discord

2. **Créer une nouvelle application**
   - C<PERSON>z sur "New Application"
   - Donnez un nom à votre bot (ex: "Vinted Bot")
   - Cliquez sur "Create"

3. **Configurer le Bot**
   - Allez dans l'onglet "Bot" (menu de gauche)
   - Cliquez sur "Add Bot"
   - Confirmez en cliquant "Yes, do it!"

4. **Récupérer le Token**
   - Dans la section "Token", cliquez sur "Copy"
   - ⚠️ **IMPORTANT** : Ne partagez JAMAIS ce token !

5. **Récupérer le Client ID**
   - Allez dans l'onglet "General Information"
   - Copiez l'"Application ID" (c'est votre Client ID)

### 2. ⚙️ Configurer le Bot

1. **Copier le fichier de configuration**
   ```bash
   copy config.example.json config.json
   ```

2. **Éditer config.json**
   ```json
   {
     "token": "COLLEZ_VOTRE_TOKEN_ICI",
     "prefix": "!",
     "clientId": "COLLEZ_VOTRE_CLIENT_ID_ICI"
   }
   ```

### 3. 🔗 Inviter le Bot sur votre Serveur

1. **Générer le lien d'invitation**
   - Dans le Developer Portal, onglet "OAuth2" > "URL Generator"
   - **Scopes** : Cochez `bot`
   - **Bot Permissions** : Cochez :
     - ✅ Send Messages
     - ✅ Use Slash Commands
     - ✅ Read Message History
     - ✅ Embed Links
     - ✅ Manage Messages

2. **Copier et utiliser l'URL**
   - Copiez l'URL générée en bas
   - Ouvrez cette URL dans votre navigateur
   - Sélectionnez votre serveur Discord
   - Cliquez sur "Autoriser"

### 4. 🚀 Démarrer le Bot

```bash
npm start
```

Vous devriez voir :
```
🤖 Bot Discord Vinted démarré!
📝 Connecté en tant que: VotreBot#1234
🏠 Présent sur 1 serveur(s)
👥 X utilisateurs accessibles
```

## 🎯 Test des Commandes

### Tester !addLien
```
!addLien https://www.vinted.fr/vetements?search_text=nike
```

### Tester !removeLien
```
!removeLien
```

## 🔧 Permissions Requises

Pour utiliser les commandes, les utilisateurs doivent avoir la permission **"Gérer les messages"** sur le serveur.

## 🆘 Résolution de Problèmes

### Le bot ne répond pas
- ✅ Vérifiez que le token est correct dans config.json
- ✅ Vérifiez que le bot est en ligne (statut vert)
- ✅ Vérifiez les permissions du bot sur le serveur

### Erreur "Invalid Token"
- ❌ Le token dans config.json est incorrect
- 🔄 Régénérez un nouveau token dans le Developer Portal

### Erreur de permissions
- ❌ Le bot n'a pas les bonnes permissions
- 🔄 Réinvitez le bot avec les bonnes permissions

### Le bot ne peut pas envoyer de messages
- ❌ Vérifiez les permissions du salon
- ✅ Le bot doit pouvoir "Envoyer des messages" et "Intégrer des liens"

## 📊 Logs et Debugging

Le bot affiche des logs détaillés :
- 🔧 Commandes exécutées
- ❌ Erreurs rencontrées
- 📊 Statistiques d'utilisation

## 🔒 Sécurité

- ⚠️ **Ne partagez JAMAIS votre token Discord**
- 🔐 Ajoutez `config.json` à votre `.gitignore`
- 🛡️ Utilisez des permissions minimales nécessaires

---

**🎉 Votre bot Discord Vinted est maintenant prêt à l'emploi !**
