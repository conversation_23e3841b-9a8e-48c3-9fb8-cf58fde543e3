# 📖 Guide d'Utilisation - Vinted Finders

## 🎯 Vue d'ensemble

Vinted Finders est un bot Discord avancé qui analyse les produits Vinted et trouve automatiquement des articles similaires au prix le plus bas. Il utilise des techniques de scraping pour extraire les informations des produits et effectuer des recherches intelligentes.

## 🚀 Fonctionnalités Principales

### 🔍 Analyse de Produits
- **Scraping automatique** des détails du produit (titre, prix, marque, taille, photos)
- **Extraction des informations utilisateur** (nom, photo, profil)
- **Détection de la couleur dominante** pour les embeds

### 💰 Recherche de Prix
- **Recherche de produits similaires** basée sur la marque et les mots-clés
- **Tri automatique par prix croissant** pour trouver les meilleures affaires
- **Filtrage intelligent** pour éviter les doublons

### 🔗 Notifications Webhook
- **Embeds riches** avec toutes les informations du produit
- **Boutons d'action** (Plus d'info, Envoyer un message, Acheter)
- **Couleurs dynamiques** basées sur la photo du produit

## 📋 Guide Étape par Étape

### 1. Configuration Initiale

#### Créer un Webhook Discord
1. Allez dans les paramètres de votre salon Discord
2. Cliquez sur "Intégrations" → "Webhooks"
3. Cliquez sur "Créer un webhook"
4. Donnez-lui un nom (ex: "Vinted Finders")
5. Copiez l'URL du webhook

#### Inviter le Bot
1. Utilisez le lien d'invitation fourni
2. Sélectionnez votre serveur
3. Accordez les permissions nécessaires

### 2. Utilisation des Commandes

#### /addlien - Analyser un Produit

**Syntaxe :**
```
/addlien produit_url:<URL_PRODUIT> webhook_url:<URL_WEBHOOK>
```

**Exemple concret :**
```
/addlien 
  produit_url:https://www.vinted.fr/items/3456789012
  webhook_url:https://discord.com/api/webhooks/1234567890/abcdef...
```

**Ce qui se passe :**
1. 🔄 Le bot analyse le produit Vinted
2. 🔍 Il recherche des produits similaires
3. 💰 Il trie les résultats par prix croissant
4. 📤 Il envoie les résultats via webhook
5. ✅ Il confirme la configuration

#### /removelien - Supprimer la Configuration

**Syntaxe :**
```
/removelien
```

**Utilisation :**
- Supprime la configuration Vinted du salon actuel
- Aucun paramètre requis
- Affiche un résumé de ce qui a été supprimé

#### /stats - Voir les Statistiques

**Syntaxe :**
```
/stats
```

**Informations affichées :**
- Nombre total de serveurs
- Configurations actives par serveur
- Statistiques d'utilisation
- État des webhooks

### 3. Format des Embeds

#### Embed Principal (Produit Analysé)
```
🎯 Produit recherché: [Titre du produit]
┌─ Auteur: Nom utilisateur Vinted
├─ 💸 Prix: 25,00 €
├─ 🏷️ Marque: Nike
├─ 📏 Taille: M
└─ 📆 Date du post: Il y a 2 heures

[Image du produit]

[➕ Plus d'info] [💬 Message] [💸 Acheter]
```

#### Embed Secondaire (Produits Similaires)
```
🔍 Produits similaires trouvés (triés par prix)
5 produit(s) similaire(s) au prix le plus bas

1. 15,00 € - Nike Air Max Taille M...
2. 18,50 € - Nike Sportswear Taille M...
3. 22,00 € - Nike Vintage Taille M...
...
```

## 🔧 Configuration Avancée

### Webhooks Multiples
Vous pouvez configurer différents webhooks pour différents salons :

```bash
# Salon #vinted-sneakers
/addlien produit_url:... webhook_url:https://discord.com/api/webhooks/111/...

# Salon #vinted-mode
/addlien produit_url:... webhook_url:https://discord.com/api/webhooks/222/...
```

### Gestion Multi-Serveurs
Le bot fonctionne automatiquement sur plusieurs serveurs :
- Chaque serveur a ses propres configurations
- Les données sont isolées par salon
- Aucune configuration supplémentaire requise

## 🛠️ Dépannage

### Problèmes Courants

#### "URL Vinted invalide"
- ✅ Vérifiez que l'URL commence par `https://www.vinted.fr/items/`
- ✅ Assurez-vous que le produit existe toujours
- ✅ Testez l'URL dans votre navigateur

#### "Webhook invalide"
- ✅ Vérifiez le format : `https://discord.com/api/webhooks/...`
- ✅ Assurez-vous que le webhook n'a pas été supprimé
- ✅ Vérifiez les permissions du webhook

#### "Impossible d'analyser le produit"
- ✅ Le produit peut avoir été supprimé
- ✅ Vinted peut temporairement bloquer les requêtes
- ✅ Réessayez dans quelques minutes

#### "Aucun produit similaire trouvé"
- ✅ Normal pour des produits très spécifiques
- ✅ Le bot recherche par marque et mots-clés
- ✅ Essayez avec un produit plus commun

### Messages d'Erreur

#### "Permission refusée"
- Vous devez avoir la permission "Gérer les messages"
- Contactez un administrateur du serveur

#### "Erreur de sauvegarde"
- Problème temporaire avec le stockage
- Réessayez la commande

#### "Erreur d'analyse"
- Le produit Vinted n'est pas accessible
- Vérifiez l'URL et réessayez

## 📊 Optimisation

### Meilleures Pratiques

1. **URLs de Produits**
   - Utilisez des URLs directes de produits (`/items/123456`)
   - Évitez les URLs de recherche ou de catégories

2. **Webhooks**
   - Créez des webhooks dédiés pour Vinted Finders
   - Utilisez des noms explicites pour les identifier

3. **Fréquence d'Utilisation**
   - Évitez de spammer les commandes
   - Attendez que l'analyse soit terminée avant de relancer

### Limites Techniques

- **Délai entre requêtes** : 1 seconde minimum
- **Produits similaires** : Maximum 10 résultats
- **Cache** : 5 minutes pour éviter les requêtes répétées
- **Timeout** : 15 secondes par requête Vinted

## 🆘 Support

### Obtenir de l'Aide

1. **Vérifiez les logs** du bot avec `/stats`
2. **Testez avec un produit simple** d'abord
3. **Vérifiez vos permissions** Discord
4. **Consultez ce guide** pour les erreurs courantes

### Signaler un Bug

Si vous rencontrez un problème persistant :
1. Notez l'URL du produit testé
2. Copiez le message d'erreur exact
3. Indiquez l'heure de l'erreur
4. Contactez l'administrateur du bot

---

## 🎉 Conclusion

Vinted Finders transforme votre expérience d'achat sur Vinted en automatisant la recherche des meilleures affaires. Avec ses fonctionnalités avancées de scraping et de comparaison de prix, vous ne manquerez plus jamais une bonne affaire !

**Bon shopping ! 🛍️**
