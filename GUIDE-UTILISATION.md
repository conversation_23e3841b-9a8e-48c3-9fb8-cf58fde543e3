# 📖 Guide d'Utilisation - Vinted Finders

## 🎯 Vue d'ensemble

Vinted Finders est un bot Discord de recherche instantanée qui vous permet de trouver les meilleures affaires sur Vinted directement depuis Discord. Plus besoin de naviguer sur le site - tapez votre recherche et obtenez les meilleurs prix immédiatement !

## 🚀 Fonctionnalités Principales

### 🔍 Recherche Instantanée
- **Recherche par mots-clés** : Tapez ce que vous cherchez (ex: "nike t-shirt")
- **Résultats en temps réel** : Obtenez les résultats directement dans Discord
- **Tri automatique** : Les produits sont triés du prix le plus bas au plus élevé

### 💰 Filtres Avancés
- **Prix maximum** : Limitez votre budget
- **Taille spécifique** : Trouvez exactement votre taille
- **Catégories** : Vêtements, chaussures, sacs, accessoires

### 🎨 Interface Intuitive
- **Embeds riches** : Informations claires et visuelles
- **Boutons d'action** : Accès direct aux produits Vinted
- **Médailles** : 🥇🥈🥉 pour les 3 meilleurs prix

## 📋 Guide Étape par Étape

### 1. Inviter le Bot

1. **Utilisez le lien d'invitation** fourni par l'administrateur
2. **Sélectionnez votre serveur** Discord
3. **Accordez les permissions** nécessaires
4. **C'est tout !** Le bot est prêt à utiliser

### 2. Utilisation Simple

#### Recherche Basique

**Syntaxe :**
```
/recherche termes:"ce que vous cherchez"
```

**Exemples :**
```
/recherche termes:"nike"
/recherche termes:"t-shirt blanc"
/recherche termes:"chaussures running"
```

#### Recherche avec Filtres

**Syntaxe :**
```
/recherche termes:"..." prix_max:XX taille:X categorie:...
```

**Exemples :**
```
/recherche termes:"nike air max" prix_max:100
/recherche termes:"robe" taille:M prix_max:50
/recherche termes:"baskets" taille:42 categorie:chaussures
```

#### /removelien - Supprimer la Configuration

**Syntaxe :**
```
/removelien
```

**Utilisation :**
- Supprime la configuration Vinted du salon actuel
- Aucun paramètre requis
- Affiche un résumé de ce qui a été supprimé

#### /stats - Voir les Statistiques

**Syntaxe :**
```
/stats
```

**Informations affichées :**
- Nombre total de serveurs
- Configurations actives par serveur
- Statistiques d'utilisation
- État des webhooks

### 3. Format des Embeds

#### Embed Principal (Produit Analysé)
```
🎯 Produit recherché: [Titre du produit]
┌─ Auteur: Nom utilisateur Vinted
├─ 💸 Prix: 25,00 €
├─ 🏷️ Marque: Nike
├─ 📏 Taille: M
└─ 📆 Date du post: Il y a 2 heures

[Image du produit]

[➕ Plus d'info] [💬 Message] [💸 Acheter]
```

#### Embed Secondaire (Produits Similaires)
```
🔍 Produits similaires trouvés (triés par prix)
5 produit(s) similaire(s) au prix le plus bas

1. 15,00 € - Nike Air Max Taille M...
2. 18,50 € - Nike Sportswear Taille M...
3. 22,00 € - Nike Vintage Taille M...
...
```

## 🔧 Configuration Avancée

### Webhooks Multiples
Vous pouvez configurer différents webhooks pour différents salons :

```bash
# Salon #vinted-sneakers
/addlien produit_url:... webhook_url:https://discord.com/api/webhooks/111/...

# Salon #vinted-mode
/addlien produit_url:... webhook_url:https://discord.com/api/webhooks/222/...
```

### Gestion Multi-Serveurs
Le bot fonctionne automatiquement sur plusieurs serveurs :
- Chaque serveur a ses propres configurations
- Les données sont isolées par salon
- Aucune configuration supplémentaire requise

## 🛠️ Dépannage

### Problèmes Courants

#### "URL Vinted invalide"
- ✅ Vérifiez que l'URL commence par `https://www.vinted.fr/items/`
- ✅ Assurez-vous que le produit existe toujours
- ✅ Testez l'URL dans votre navigateur

#### "Webhook invalide"
- ✅ Vérifiez le format : `https://discord.com/api/webhooks/...`
- ✅ Assurez-vous que le webhook n'a pas été supprimé
- ✅ Vérifiez les permissions du webhook

#### "Impossible d'analyser le produit"
- ✅ Le produit peut avoir été supprimé
- ✅ Vinted peut temporairement bloquer les requêtes
- ✅ Réessayez dans quelques minutes

#### "Aucun produit similaire trouvé"
- ✅ Normal pour des produits très spécifiques
- ✅ Le bot recherche par marque et mots-clés
- ✅ Essayez avec un produit plus commun

### Messages d'Erreur

#### "Permission refusée"
- Vous devez avoir la permission "Gérer les messages"
- Contactez un administrateur du serveur

#### "Erreur de sauvegarde"
- Problème temporaire avec le stockage
- Réessayez la commande

#### "Erreur d'analyse"
- Le produit Vinted n'est pas accessible
- Vérifiez l'URL et réessayez

## 📊 Optimisation

### Meilleures Pratiques

1. **URLs de Produits**
   - Utilisez des URLs directes de produits (`/items/123456`)
   - Évitez les URLs de recherche ou de catégories

2. **Webhooks**
   - Créez des webhooks dédiés pour Vinted Finders
   - Utilisez des noms explicites pour les identifier

3. **Fréquence d'Utilisation**
   - Évitez de spammer les commandes
   - Attendez que l'analyse soit terminée avant de relancer

### Limites Techniques

- **Délai entre requêtes** : 1 seconde minimum
- **Produits similaires** : Maximum 10 résultats
- **Cache** : 5 minutes pour éviter les requêtes répétées
- **Timeout** : 15 secondes par requête Vinted

## 🆘 Support

### Obtenir de l'Aide

1. **Vérifiez les logs** du bot avec `/stats`
2. **Testez avec un produit simple** d'abord
3. **Vérifiez vos permissions** Discord
4. **Consultez ce guide** pour les erreurs courantes

### Signaler un Bug

Si vous rencontrez un problème persistant :
1. Notez l'URL du produit testé
2. Copiez le message d'erreur exact
3. Indiquez l'heure de l'erreur
4. Contactez l'administrateur du bot

---

## 🎉 Conclusion

Vinted Finders transforme votre expérience d'achat sur Vinted en automatisant la recherche des meilleures affaires. Avec ses fonctionnalités avancées de scraping et de comparaison de prix, vous ne manquerez plus jamais une bonne affaire !

**Bon shopping ! 🛍️**
