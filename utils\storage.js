const fs = require('fs').promises;
const path = require('path');

class Storage {
    constructor() {
        this.dataPath = path.join(__dirname, '..', 'data', 'links.json');
    }

    /**
     * Charge les données depuis le fichier JSON
     * @returns {Promise<Object>} Les données chargées
     */
    async loadData() {
        try {
            const data = await fs.readFile(this.dataPath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            if (error.code === 'ENOENT') {
                // Fichier n'existe pas, retourner un objet vide
                return {};
            }
            console.error('Erreur lors du chargement des données:', error);
            return {};
        }
    }

    /**
     * Sauvegarde les données dans le fichier JSON
     * @param {Object} data - Les données à sauvegarder
     * @returns {Promise<boolean>} Succès de la sauvegarde
     */
    async saveData(data) {
        try {
            await fs.writeFile(this.dataPath, JSON.stringify(data, null, 2), 'utf8');
            return true;
        } catch (error) {
            console.error('Erreur lors de la sauvegarde des données:', error);
            return false;
        }
    }

    /**
     * Ajoute un lien Vinted pour un salon
     * @param {string} channelId - ID du salon Discord
     * @param {string} vintedUrl - URL Vinted à surveiller
     * @returns {Promise<boolean>} Succès de l'ajout
     */
    async addLink(channelId, vintedUrl) {
        try {
            const data = await this.loadData();
            data[channelId] = {
                url: vintedUrl,
                addedAt: new Date().toISOString(),
                lastChecked: null
            };
            return await this.saveData(data);
        } catch (error) {
            console.error('Erreur lors de l\'ajout du lien:', error);
            return false;
        }
    }

    /**
     * Supprime un lien Vinted pour un salon
     * @param {string} channelId - ID du salon Discord
     * @returns {Promise<boolean>} Succès de la suppression
     */
    async removeLink(channelId) {
        try {
            const data = await this.loadData();
            if (data[channelId]) {
                delete data[channelId];
                return await this.saveData(data);
            }
            return false; // Aucun lien à supprimer
        } catch (error) {
            console.error('Erreur lors de la suppression du lien:', error);
            return false;
        }
    }

    /**
     * Récupère le lien Vinted pour un salon
     * @param {string} channelId - ID du salon Discord
     * @returns {Promise<Object|null>} Les données du lien ou null
     */
    async getLink(channelId) {
        try {
            const data = await this.loadData();
            return data[channelId] || null;
        } catch (error) {
            console.error('Erreur lors de la récupération du lien:', error);
            return null;
        }
    }

    /**
     * Récupère tous les liens enregistrés
     * @returns {Promise<Object>} Tous les liens
     */
    async getAllLinks() {
        return await this.loadData();
    }
}

module.exports = Storage;
