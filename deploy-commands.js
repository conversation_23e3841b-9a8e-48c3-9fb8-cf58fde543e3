const { REST, Routes } = require('discord.js');
const fs = require('node:fs');
const path = require('node:path');
const config = require('./config.json');

const commands = [];

// Récupération de tous les fichiers de commandes
const foldersPath = path.join(__dirname, 'commands');

// Vérifier si le dossier commands existe
if (fs.existsSync(foldersPath)) {
    const commandFolders = fs.readdirSync(foldersPath);

    for (const folder of commandFolders) {
        const commandsPath = path.join(foldersPath, folder);

        // Vérifier si c'est un dossier
        if (fs.statSync(commandsPath).isDirectory()) {
            const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

            for (const file of commandFiles) {
                const filePath = path.join(commandsPath, file);
                const command = require(filePath);

                if ('data' in command && 'execute' in command) {
                    commands.push(command.data.toJSON());
                    console.log(`📝 Commande ajoutée: /${command.data.name}`);
                } else {
                    console.log(`[ATTENTION] La commande ${filePath} n'a pas de propriété "data" ou "execute" requise.`);
                }
            }
        }
    }
} else {
    console.error('❌ Le dossier commands n\'existe pas !');
    process.exit(1);
}

// Construction et préparation de l'instance REST
const rest = new REST().setToken(config.token);

// Déploiement des commandes
(async () => {
    try {
        console.log(`🚀 Démarrage du rafraîchissement de ${commands.length} commande(s) slash.`);

        // Méthode pour déployer globalement les commandes
        // await rest.put(Routes.applicationCommands(config.clientId), { body: commands });

        // Méthode pour déployer les commandes sur un serveur spécifique (plus rapide pour les tests)
        if (config.guildId && config.guildId !== 'VOTRE_GUILD_ID_TEST_ICI') {
            const data = await rest.put(
                Routes.applicationGuildCommands(config.clientId, config.guildId),
                { body: commands },
            );
            console.log(`✅ ${data.length} commande(s) slash rechargée(s) avec succès sur le serveur de test.`);
        } else {
            // Déploiement global si pas de guild ID
            const data = await rest.put(
                Routes.applicationCommands(config.clientId),
                { body: commands },
            );
            console.log(`✅ ${data.length} commande(s) slash rechargée(s) avec succès globalement.`);
        }

    } catch (error) {
        console.error('❌ Erreur lors du déploiement des commandes:', error);
    }
})();
