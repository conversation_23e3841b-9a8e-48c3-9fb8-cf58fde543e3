const { EmbedBuilder } = require('discord.js');
const Storage = require('../utils/storage');

module.exports = {
    name: 'removeLien',
    description: 'Supprime le lien Vinted configuré pour ce salon',
    usage: '!removeLien',
    
    async execute(message, args) {
        try {
            // Vérification des permissions
            if (!message.member.permissions.has('ManageMessages')) {
                const errorEmbed = new EmbedBuilder()
                    .setColor(0xff0000)
                    .setTitle('❌ Permission refusée')
                    .setDescription('Vous devez avoir la permission "Gérer les messages" pour utiliser cette commande.')
                    .setTimestamp();
                
                return message.reply({ embeds: [errorEmbed] });
            }

            const channelId = message.channel.id;
            const storage = new Storage();

            // Vérification si un lien existe pour ce salon
            const existingLink = await storage.getLink(channelId);
            
            if (!existingLink) {
                const infoEmbed = new EmbedBuilder()
                    .setColor(0xffaa00)
                    .setTitle('ℹ️ Aucun lien configuré')
                    .setDescription('Aucun lien Vinted n\'est configuré pour ce salon.')
                    .addFields({
                        name: 'Action suggérée',
                        value: 'Utilisez `!addLien <url_vinted>` pour ajouter un lien à surveiller.',
                        inline: false
                    })
                    .setTimestamp();
                
                return message.reply({ embeds: [infoEmbed] });
            }

            // Message de traitement
            const processingMessage = await message.reply('🔄 Suppression du lien en cours...');

            // Suppression du lien
            const removed = await storage.removeLink(channelId);
            
            if (!removed) {
                const errorEmbed = new EmbedBuilder()
                    .setColor(0xff0000)
                    .setTitle('❌ Erreur de suppression')
                    .setDescription('Une erreur est survenue lors de la suppression du lien.')
                    .setTimestamp();
                
                return processingMessage.edit({ content: '', embeds: [errorEmbed] });
            }

            // Création de l'embed de succès
            const successEmbed = new EmbedBuilder()
                .setColor(0x00ff00)
                .setTitle('✅ Lien supprimé')
                .setDescription('Le lien Vinted a été supprimé avec succès de ce salon.')
                .addFields(
                    {
                        name: '🗑️ Lien supprimé',
                        value: existingLink.url,
                        inline: false
                    },
                    {
                        name: '📍 Salon',
                        value: `<#${channelId}>`,
                        inline: true
                    },
                    {
                        name: '📅 Ajouté le',
                        value: new Date(existingLink.addedAt).toLocaleDateString('fr-FR', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                        }),
                        inline: true
                    }
                )
                .setFooter({
                    text: 'Vinted Bot'
                })
                .setTimestamp();

            await processingMessage.edit({ content: '', embeds: [successEmbed] });

        } catch (error) {
            console.error('Erreur dans la commande removeLien:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor(0xff0000)
                .setTitle('❌ Erreur interne')
                .setDescription('Une erreur inattendue s\'est produite. Veuillez réessayer plus tard.')
                .setTimestamp();
            
            message.reply({ embeds: [errorEmbed] });
        }
    }
};
