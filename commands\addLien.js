const { EmbedBuilder } = require('discord.js');
const Storage = require('../utils/storage');
const VintedAPI = require('../utils/vinted');

module.exports = {
    name: 'addLien',
    description: 'Ajoute un lien Vinted à surveiller pour ce salon',
    usage: '!addLien <url_vinted>',
    
    async execute(message, args) {
        try {
            // Vérification des permissions
            if (!message.member.permissions.has('ManageMessages')) {
                const errorEmbed = new EmbedBuilder()
                    .setColor(0xff0000)
                    .setTitle('❌ Permission refusée')
                    .setDescription('Vous devez avoir la permission "Gérer les messages" pour utiliser cette commande.')
                    .setTimestamp();
                
                return message.reply({ embeds: [errorEmbed] });
            }

            // Vérification des arguments
            if (!args.length) {
                const helpEmbed = new EmbedBuilder()
                    .setColor(0xffaa00)
                    .setTitle('📝 Utilisation de la commande')
                    .setDescription('**Usage:** `!addLien <url_vinted>`')
                    .addFields(
                        {
                            name: 'Exemple',
                            value: '`!addLien https://www.vinted.fr/vetements?search_text=nike`',
                            inline: false
                        },
                        {
                            name: 'Note',
                            value: 'L\'URL doit être un lien de recherche Vinted valide.',
                            inline: false
                        }
                    )
                    .setTimestamp();
                
                return message.reply({ embeds: [helpEmbed] });
            }

            const url = args.join(' ').trim();
            const channelId = message.channel.id;
            
            // Initialisation des services
            const storage = new Storage();
            const vintedAPI = new VintedAPI();

            // Validation de l'URL Vinted
            if (!vintedAPI.isValidVintedUrl(url)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor(0xff0000)
                    .setTitle('❌ URL invalide')
                    .setDescription('L\'URL fournie n\'est pas un lien Vinted valide.')
                    .addFields({
                        name: 'Formats acceptés',
                        value: '• `https://www.vinted.fr/...`\n• `https://vinted.fr/...`\n• Autres domaines Vinted (.com, .de, .it, etc.)',
                        inline: false
                    })
                    .setTimestamp();
                
                return message.reply({ embeds: [errorEmbed] });
            }

            // Vérification si un lien existe déjà pour ce salon
            const existingLink = await storage.getLink(channelId);
            if (existingLink) {
                const warningEmbed = new EmbedBuilder()
                    .setColor(0xffaa00)
                    .setTitle('⚠️ Lien existant')
                    .setDescription('Un lien Vinted est déjà configuré pour ce salon.')
                    .addFields(
                        {
                            name: 'Lien actuel',
                            value: existingLink.url,
                            inline: false
                        },
                        {
                            name: 'Action',
                            value: 'Utilisez `!removeLien` pour supprimer le lien actuel avant d\'en ajouter un nouveau.',
                            inline: false
                        }
                    )
                    .setTimestamp();
                
                return message.reply({ embeds: [warningEmbed] });
            }

            // Message de traitement
            const processingMessage = await message.reply('🔄 Vérification du lien Vinted en cours...');

            // Vérification de l'accessibilité de l'URL
            const isAccessible = await vintedAPI.checkUrlAccessibility(url);
            if (!isAccessible) {
                const errorEmbed = new EmbedBuilder()
                    .setColor(0xff0000)
                    .setTitle('❌ Lien inaccessible')
                    .setDescription('Le lien Vinted fourni n\'est pas accessible. Vérifiez l\'URL et réessayez.')
                    .setTimestamp();
                
                return processingMessage.edit({ content: '', embeds: [errorEmbed] });
            }

            // Extraction des informations du lien
            const vintedInfo = await vintedAPI.extractSearchInfo(url);
            
            // Sauvegarde du lien
            const saved = await storage.addLink(channelId, url);
            
            if (!saved) {
                const errorEmbed = new EmbedBuilder()
                    .setColor(0xff0000)
                    .setTitle('❌ Erreur de sauvegarde')
                    .setDescription('Une erreur est survenue lors de la sauvegarde du lien.')
                    .setTimestamp();
                
                return processingMessage.edit({ content: '', embeds: [errorEmbed] });
            }

            // Création de l'embed de succès
            const successEmbed = vintedAPI.formatForDiscord(vintedInfo);
            successEmbed.addFields({
                name: '📍 Salon configuré',
                value: `<#${channelId}>`,
                inline: true
            });

            await processingMessage.edit({ content: '', embeds: [successEmbed] });

        } catch (error) {
            console.error('Erreur dans la commande addLien:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor(0xff0000)
                .setTitle('❌ Erreur interne')
                .setDescription('Une erreur inattendue s\'est produite. Veuillez réessayer plus tard.')
                .setTimestamp();
            
            message.reply({ embeds: [errorEmbed] });
        }
    }
};
