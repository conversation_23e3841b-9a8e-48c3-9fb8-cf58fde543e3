# 🤖 Bot Discord Vinted

Un bot Discord simple et efficace pour surveiller les liens Vinted par salon.

## 📋 Fonctionnalités

- **!addLien** - Ajoute un lien Vinted à surveiller pour le salon actuel
- **!removeLien** - Supprime le lien Vinted configuré pour le salon
- **!stats** - Affiche les statistiques du bot (multi-serveurs)
- **Multi-Guild** - Fonctionne simultanément sur plusieurs serveurs Discord
- **Stockage JSON** - Sauvegarde persistante des liens par salon
- **Validation d'URLs** - Vérification automatique des liens Vinted
- **Interface moderne** - Embeds Discord élégants et informatifs

## 🚀 Installation

### Prérequis
- Node.js 16.0.0 ou supérieur
- Un bot Discord configuré

### Étapes d'installation

1. **Cloner le projet**
   ```bash
   git clone <votre-repo>
   cd vinted-discord-bot
   ```

2. **Installer les dépendances**
   ```bash
   npm install
   ```

3. **Configuration**
   - Renommez `config.json` et ajoutez vos informations :
   ```json
   {
     "token": "VOTRE_TOKEN_DISCORD",
     "prefix": "!",
     "clientId": "VOTRE_CLIENT_ID"
   }
   ```

4. **Démarrer le bot**
   ```bash
   npm start
   ```

## 🔧 Configuration du Bot Discord

### Créer une application Discord

1. Allez sur [Discord Developer Portal](https://discord.com/developers/applications)
2. Cliquez sur "New Application"
3. Donnez un nom à votre bot
4. Allez dans l'onglet "Bot"
5. Cliquez sur "Add Bot"
6. Copiez le token et ajoutez-le dans `config.json`

### Inviter le bot sur votre serveur

1. Dans l'onglet "OAuth2" > "URL Generator"
2. Sélectionnez les scopes : `bot`
3. Sélectionnez les permissions :
   - Send Messages
   - Use Slash Commands
   - Read Message History
   - Embed Links
   - Manage Messages (pour les commandes)
4. Utilisez l'URL générée pour inviter le bot

## 📖 Utilisation

### Commandes disponibles

#### !addLien
Ajoute un lien Vinted à surveiller pour le salon actuel.

**Usage :** `!addLien <url_vinted>`

**Exemple :**
```
!addLien https://www.vinted.fr/vetements?search_text=nike&brand_ids[]=53
```

**Permissions requises :** Gérer les messages

#### !removeLien
Supprime le lien Vinted configuré pour le salon actuel.

**Usage :** `!removeLien`

**Permissions requises :** Gérer les messages

#### !stats
Affiche les statistiques globales du bot et le détail par serveur.

**Usage :** `!stats`

**Permissions requises :** Aucune (accessible à tous)

## 🌐 Fonctionnement Multi-Serveurs

Le bot est **nativement conçu pour fonctionner sur plusieurs serveurs Discord simultanément** :

### ✅ Avantages Multi-Guild
- **Une seule instance** du bot pour tous vos serveurs
- **Gestion indépendante** des liens par salon
- **Stockage centralisé** mais données isolées
- **Scalabilité illimitée** - ajoutez autant de serveurs que vous voulez

### 🏗️ Architecture
```
Bot Vinted (Instance Unique)
├── 🎮 Serveur Gaming
│   ├── #vinted-deals → Lien Nike
│   └── #sneakers → Lien Jordan
├── 👗 Serveur Mode
│   ├── #fashion → Lien Designer
│   └── #vintage → Lien Vintage
└── 🎓 Serveur Étudiants
    └── #bon-plans → Lien < 20€
```

### 🚀 Utilisation Multi-Serveurs
1. **Invitez le bot** sur chaque serveur avec le même lien
2. **Configurez des liens différents** dans chaque salon
3. **Le bot gère automatiquement** tous les serveurs

### Formats d'URLs supportés

Le bot accepte tous les domaines Vinted officiels :
- `vinted.fr` (France)
- `vinted.com` (International)
- `vinted.de` (Allemagne)
- `vinted.it` (Italie)
- `vinted.es` (Espagne)
- Et autres domaines Vinted

## 📁 Structure du projet

```
vinted-discord-bot/
├── commands/           # Commandes du bot
│   ├── addLien.js     # Commande !addLien
│   └── removeLien.js  # Commande !removeLien
├── utils/             # Utilitaires
│   ├── storage.js     # Gestion du stockage JSON
│   └── vinted.js      # API Vinted
├── data/              # Données
│   └── links.json     # Stockage des liens
├── config.json        # Configuration
├── index.js           # Point d'entrée
├── package.json       # Dépendances
└── README.md          # Documentation
```

## 🛠️ Développement

### Scripts disponibles

- `npm start` - Démarre le bot
- `npm run dev` - Démarre le bot (alias)

### Dépendances

- **discord.js** - Bibliothèque Discord officielle
- **axios** - Client HTTP pour les requêtes
- **cheerio** - Parser HTML pour l'extraction de données

## 🔒 Sécurité

- Validation stricte des URLs Vinted
- Vérification des permissions utilisateur
- Gestion d'erreurs complète
- Logs détaillés pour le debugging

## 📝 Logs

Le bot génère des logs détaillés :
- Démarrage et connexion
- Exécution des commandes
- Erreurs et avertissements
- Statistiques d'utilisation

## 🤝 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à :
- Signaler des bugs
- Proposer des améliorations
- Soumettre des pull requests

## 📄 Licence

MIT License - Voir le fichier LICENSE pour plus de détails.

## 🆘 Support

En cas de problème :
1. Vérifiez les logs du bot
2. Assurez-vous que la configuration est correcte
3. Vérifiez les permissions du bot sur Discord
4. Consultez la documentation Discord.js

---

**Développé avec ❤️ pour la communauté Vinted**
